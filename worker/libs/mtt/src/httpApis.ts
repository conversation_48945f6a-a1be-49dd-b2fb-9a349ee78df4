import { commonProto } from './mtt/pb/commonProto';
import axios from 'axios';
import { getProxyAgent, logging, LoginError } from 'shared';

axios.defaults.timeout = 20_000; // 20 seconds

export class HttpApis {
    mttApiUrl: string;

    constructor(mttApiUrl: string) {
        this.mttApiUrl = mttApiUrl;
        axios.defaults.httpsAgent = getProxyAgent();
    }

    async requestMttTournamentList(token, platform) {
        const { Mtt_Tournament_List_Request, Mtt_Tournament_List_Response } = commonProto;

        let req = new Mtt_Tournament_List_Request();
        req.PlatForm = platform;
        let reqData = Mtt_Tournament_List_Request.encode(req).finish();
        logging.withTag('SCAN_DEBUG').info('requestMttTournamentList req');

        const response = await this._sendGetRequest(
            `${this.mttApiUrl}/api/mtt/tournamentList?token=${token}`,
            reqData,
            Mtt_Tournament_List_Response,
        );
        logging
            .withTag('SCAN_DEBUG')
            .info('requestMttTournamentList response', JSON.stringify(response).substring(0, 100));
        return response;
    }

    async requestMttTournamentDetail(token, mttId) {
        const { MttTournamentDetailRequest, MttTournamentDetailResponse } = commonProto;
        let req = new MttTournamentDetailRequest();
        req.TournamentId = mttId;
        let reqData = MttTournamentDetailRequest.encode(req).finish();

        return this._sendGetRequest(
            `${this.mttApiUrl}/api/mtt/tournamentDetail?token=${token}`,
            reqData,
            MttTournamentDetailResponse,
        );
    }

    async requestMttPlayerSignUp(inputData): commonProto.IMttPlayerSignupResponse {
        const { MttPlayerSignupRequest, MttPlayerSignupResponse } = commonProto;

        let req = new MttPlayerSignupRequest();
        req.TournamentId = inputData.TournamentId;
        req.UserId = inputData.UserId;
        req.TicketId = inputData.TicketId;
        req.UserToken = inputData.UserToken;
        req.PlatForm = inputData.PlatForm;
        req.RegGoldType = inputData.RegGoldType;
        let reqData = MttPlayerSignupRequest.encode(req).finish();

        return this._sendGetRequest(`${this.mttApiUrl}/api/mtt/signup`, reqData, MttPlayerSignupResponse);
    }

    async requestMttReenter(
        token: string,
        tournamentId: number,
        userId: number,
        ticketId: number,
    ): Promise<commonProto.IMttReenterResponse | null> {
        const { MttReenterRequest, MttReenterResponse } = commonProto;

        let req = new MttReenterRequest();
        req.TournamentId = tournamentId;
        req.UserId = userId;
        req.UserToken = token;
        req.TicketId = ticketId;
        req.PlatForm = commonProto.PLATFORM.TRIBAL_PIONEER;
        req.RegGoldType = 0;
        let reqData = MttReenterRequest.encode(req).finish();

        return this._sendGetRequest(`${this.mttApiUrl}/api/mtt/reenter`, reqData, MttReenterResponse);
    }

    async requestPlayerTicketsData(user_token: string): Promise<commonProto.IUser_Tool_In_Backpacks_Response | null> {
        const { User_Tool_In_Backpacks_Request, User_Tool_In_Backpacks_Response } = commonProto;
        let req = new User_Tool_In_Backpacks_Request();
        let reqData = User_Tool_In_Backpacks_Request.encode(req).finish();

        return this._sendGetRequest(
            `${this.mttApiUrl}/api/dataManager/userToolInBackPack?token=${user_token}`,
            reqData,
            User_Tool_In_Backpacks_Response,
        );
    }

    async requestMttTournamentStatus(token, mttId) {
        const { MttTournamentStatusRequest, MttTournamentStatusResponse } = commonProto;
        let req = new MttTournamentStatusRequest();
        req.TournamentId = mttId;
        let reqData = MttTournamentStatusRequest.encode(req).finish();

        return this._sendGetRequest(
            `${this.mttApiUrl}/api/mtt/tournamentStatus?token=${token}`,
            reqData,
            MttTournamentStatusResponse,
        );
    }

    async requestMttMultiTable(token, userId) {
        const { MttMultiTableRequest, MttMultiTableResponse } = commonProto;
        let req = new MttMultiTableRequest();
        req.UserId = userId;
        let reqData = MttMultiTableRequest.encode(req).finish();

        return this._sendGetRequest(
            `${this.mttApiUrl}/api/mtt/multiTablePlayers?token=${token}`,
            reqData,
            MttMultiTableResponse,
        );
    }

    async requestJoinedTournaments(token: string): Promise<commonProto.IPlayer_Joined_Tournaments_Response> {
        const { Player_Joined_Tournaments_Request, Player_Joined_Tournaments_Response } = commonProto;
        let req = new Player_Joined_Tournaments_Request();
        let reqData = Player_Joined_Tournaments_Request.encode(req).finish();
        logging.info('requestJoinedTournaments req');

        return this._sendGetRequest(
            `${this.mttApiUrl}/api/mtt/playerJoinedTournaments?token=${token}`,
            reqData,
            Player_Joined_Tournaments_Response,
        );
    }

    async requestMttTournamentPlayers(token, tournamentId, nickname) {
        const { MttTournamentPlayersRequest, MttTournamentPlayersResponse } = commonProto;

        let req = new MttTournamentPlayersRequest();
        req.TournamentId = tournamentId;
        req.SearchKeyword = nickname;
        let reqData = MttTournamentPlayersRequest.encode(req).finish();
        logging.info('requestMttTournamentPlayers req', req);

        return this._sendGetRequest(
            `${this.mttApiUrl}/api/mtt/tournamentPlayers?token=${token}`,
            reqData,
            MttTournamentPlayersResponse,
        );
    }

    async _sendGetRequest(url, body, ResponseType) {
        let result;

        try {
            const rawResponse = await axios.get(url, {
                responseType: 'arraybuffer',
                data: body,
            });
            let response = rawResponse.data;

            if (!response) {
                return null;
            }

            result = ResponseType.decode(new Uint8Array(response));
        } catch (error) {
            logging.error(`Error in _sendRequest to ${url}`, error);
            throw error;
        }

        if (result.ErrorCode === commonProto.ErrorCode.Secure_Token_Invalid) {
            throw new LoginError('Invalid token');
        }

        return result;
    }
}
